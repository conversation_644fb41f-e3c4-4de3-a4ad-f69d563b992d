/**
 * Police Stations API Layer
 * 
 * This module provides API functions for police station data.
 * Currently uses static JSON data but designed for easy migration to real backend APIs.
 * 
 * Migration Path:
 * 1. Replace static imports with HTTP fetch calls
 * 2. Update base URL to point to actual backend
 * 3. Add authentication headers if required
 * 4. Keep the same function signatures and return types
 */

import policeStationsData from '../data/police-stations.json';

// TypeScript interfaces for type safety
export interface Coordinates {
  lat: number;
  lng: number;
}

export interface Address {
  street: string;
  district: string;
  city: string;
  postalCode: string;
  full: string;
}

export interface Contact {
  phone: string;
  emergency: string;
  fax: string;
}

export interface Operational {
  status: 'active' | 'inactive' | 'maintenance';
  hours: string;
  officers: number;
  capacity: number;
  established: string;
}

export interface PoliceStation {
  id: string;
  name: string;
  nameAr: string;
  coordinates: Coordinates;
  address: Address;
  addressAr: Address;
  contact: Contact;
  operational: Operational;
  services: string[];
}

export interface PoliceStationsResponse {
  metadata: {
    city: string;
    country: string;
    totalStations: number;
    lastUpdated: string;
    coordinateSystem: string;
  };
  stations: PoliceStation[];
}

export interface StationFilters {
  status?: 'active' | 'inactive' | 'maintenance';
  district?: string;
  services?: string[];
  minOfficers?: number;
  maxOfficers?: number;
}

export interface LocationBounds {
  north: number;
  south: number;
  east: number;
  west: number;
}

/**
 * Get all police stations with optional filtering
 * 
 * @param filters - Optional filters to apply
 * @returns Promise<PoliceStationsResponse>
 */
export async function getPoliceStations(filters?: StationFilters): Promise<PoliceStationsResponse> {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 300));

  try {
    let filteredStations = [...policeStationsData.stations];

    // Apply filters if provided
    if (filters) {
      if (filters.status) {
        filteredStations = filteredStations.filter(
          station => station.operational.status === filters.status
        );
      }

      if (filters.district) {
        filteredStations = filteredStations.filter(
          station => station.address.district.toLowerCase().includes(filters.district!.toLowerCase())
        );
      }

      if (filters.services && filters.services.length > 0) {
        filteredStations = filteredStations.filter(
          station => filters.services!.some(service => station.services.includes(service))
        );
      }

      if (filters.minOfficers !== undefined) {
        filteredStations = filteredStations.filter(
          station => station.operational.officers >= filters.minOfficers!
        );
      }

      if (filters.maxOfficers !== undefined) {
        filteredStations = filteredStations.filter(
          station => station.operational.officers <= filters.maxOfficers!
        );
      }
    }

    return {
      metadata: {
        ...policeStationsData.metadata,
        totalStations: filteredStations.length
      },
      stations: filteredStations
    };
  } catch (error) {
    console.error('Error fetching police stations:', error);
    throw new Error('Failed to fetch police stations data');
  }
}

/**
 * Get a specific police station by ID
 * 
 * @param stationId - The ID of the station to retrieve
 * @returns Promise<PoliceStation | null>
 */
export async function getPoliceStationById(stationId: string): Promise<PoliceStation | null> {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 200));

  try {
    const station = policeStationsData.stations.find(s => s.id === stationId);
    return station || null;
  } catch (error) {
    console.error('Error fetching police station:', error);
    throw new Error(`Failed to fetch police station with ID: ${stationId}`);
  }
}

/**
 * Get police stations within a geographic boundary
 * 
 * @param bounds - Geographic boundaries to search within
 * @returns Promise<PoliceStation[]>
 */
export async function getPoliceStationsInBounds(bounds: LocationBounds): Promise<PoliceStation[]> {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 250));

  try {
    const stationsInBounds = policeStationsData.stations.filter(station => {
      const { lat, lng } = station.coordinates;
      return (
        lat >= bounds.south &&
        lat <= bounds.north &&
        lng >= bounds.west &&
        lng <= bounds.east
      );
    });

    return stationsInBounds;
  } catch (error) {
    console.error('Error fetching stations in bounds:', error);
    throw new Error('Failed to fetch stations within specified bounds');
  }
}

/**
 * Get police stations near a specific coordinate
 * 
 * @param lat - Latitude
 * @param lng - Longitude
 * @param radiusKm - Search radius in kilometers (default: 5)
 * @returns Promise<PoliceStation[]>
 */
export async function getNearbyPoliceStations(
  lat: number, 
  lng: number, 
  radiusKm: number = 5
): Promise<PoliceStation[]> {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 300));

  try {
    // Simple distance calculation (Haversine formula)
    const calculateDistance = (lat1: number, lng1: number, lat2: number, lng2: number): number => {
      const R = 6371; // Earth's radius in kilometers
      const dLat = (lat2 - lat1) * Math.PI / 180;
      const dLng = (lng2 - lng1) * Math.PI / 180;
      const a = 
        Math.sin(dLat/2) * Math.sin(dLat/2) +
        Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
        Math.sin(dLng/2) * Math.sin(dLng/2);
      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
      return R * c;
    };

    const nearbyStations = policeStationsData.stations
      .filter(station => {
        const distance = calculateDistance(
          lat, lng, 
          station.coordinates.lat, 
          station.coordinates.lng
        );
        return distance <= radiusKm;
      })
      .sort((a, b) => {
        const distanceA = calculateDistance(lat, lng, a.coordinates.lat, a.coordinates.lng);
        const distanceB = calculateDistance(lat, lng, b.coordinates.lat, b.coordinates.lng);
        return distanceA - distanceB;
      });

    return nearbyStations;
  } catch (error) {
    console.error('Error fetching nearby stations:', error);
    throw new Error('Failed to fetch nearby police stations');
  }
}

/**
 * Get available services across all police stations
 * 
 * @returns Promise<string[]>
 */
export async function getAvailableServices(): Promise<string[]> {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 100));

  try {
    const allServices = new Set<string>();
    policeStationsData.stations.forEach(station => {
      station.services.forEach(service => allServices.add(service));
    });

    return Array.from(allServices).sort();
  } catch (error) {
    console.error('Error fetching available services:', error);
    throw new Error('Failed to fetch available services');
  }
}

/**
 * Get station statistics
 * 
 * @returns Promise<StationStatistics>
 */
export interface StationStatistics {
  totalStations: number;
  totalOfficers: number;
  averageOfficersPerStation: number;
  stationsByStatus: Record<string, number>;
  stationsByDistrict: Record<string, number>;
  mostCommonServices: Array<{ service: string; count: number }>;
}

export async function getStationStatistics(): Promise<StationStatistics> {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 200));

  try {
    const stations = policeStationsData.stations;
    const totalStations = stations.length;
    const totalOfficers = stations.reduce((sum, station) => sum + station.operational.officers, 0);
    
    // Group by status
    const stationsByStatus = stations.reduce((acc, station) => {
      acc[station.operational.status] = (acc[station.operational.status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Group by district
    const stationsByDistrict = stations.reduce((acc, station) => {
      acc[station.address.district] = (acc[station.address.district] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Count services
    const serviceCounts = stations.reduce((acc, station) => {
      station.services.forEach(service => {
        acc[service] = (acc[service] || 0) + 1;
      });
      return acc;
    }, {} as Record<string, number>);

    const mostCommonServices = Object.entries(serviceCounts)
      .map(([service, count]) => ({ service, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    return {
      totalStations,
      totalOfficers,
      averageOfficersPerStation: Math.round(totalOfficers / totalStations),
      stationsByStatus,
      stationsByDistrict,
      mostCommonServices
    };
  } catch (error) {
    console.error('Error fetching station statistics:', error);
    throw new Error('Failed to fetch station statistics');
  }
}

// Future migration helpers
export const API_CONFIG = {
  // When migrating to real backend, update these values
  BASE_URL: process.env.NEXT_PUBLIC_API_BASE_URL || '/api',
  ENDPOINTS: {
    STATIONS: '/police-stations',
    STATION_BY_ID: '/police-stations/:id',
    STATIONS_IN_BOUNDS: '/police-stations/bounds',
    NEARBY_STATIONS: '/police-stations/nearby',
    SERVICES: '/police-stations/services',
    STATISTICS: '/police-stations/statistics'
  },
  // Add authentication headers when needed
  getHeaders: () => ({
    'Content-Type': 'application/json',
    // 'Authorization': `Bearer ${getAuthToken()}`,
  })
};

/**
 * Example of how to migrate to real backend API:
 * 
 * export async function getPoliceStations(filters?: StationFilters): Promise<PoliceStationsResponse> {
 *   const url = new URL(`${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.STATIONS}`);
 *   
 *   // Add query parameters
 *   if (filters) {
 *     Object.entries(filters).forEach(([key, value]) => {
 *       if (value !== undefined) {
 *         url.searchParams.append(key, String(value));
 *       }
 *     });
 *   }
 * 
 *   const response = await fetch(url.toString(), {
 *     method: 'GET',
 *     headers: API_CONFIG.getHeaders(),
 *   });
 * 
 *   if (!response.ok) {
 *     throw new Error(`HTTP error! status: ${response.status}`);
 *   }
 * 
 *   return response.json();
 * }
 */
