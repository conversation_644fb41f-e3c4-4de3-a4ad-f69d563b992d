/**
 * Shipment Alerts API Layer
 * 
 * This module provides HTTP-like API functions for shipment alerts data.
 * Currently reads from static JSON files, but can be easily replaced with
 * actual backend API calls in the future.
 * 
 * Usage:
 * - Import the functions you need
 * - Call them as async functions
 * - Handle the returned data or errors
 */

import alertsData from '../data/shipment-alerts.json';

// Types for shipment alerts
export interface AlertSummary {
  totalAlerts: number;
  criticalAlerts: number;
  warningAlerts: number;
  infoAlerts: number;
  resolvedToday: number;
  pendingAlerts: number;
}

export interface ShipmentAlert {
  id: string;
  shipmentId: string;
  type: 'critical' | 'warning' | 'info';
  title: string;
  description: string;
  timestamp: string;
  location: string;
  status: 'pending' | 'investigating' | 'resolved' | 'monitoring';
  priority: 'high' | 'medium' | 'low';
  assignedTo: string;
  estimatedResolution: string;
}

export interface AlertsResponse {
  summary: AlertSummary;
  alerts: ShipmentAlert[];
}

export interface SearchFilters {
  query?: string;
  type?: string;
  status?: string;
  priority?: string;
}

/**
 * Simulates API delay for realistic behavior (disabled for static data)
 */
const simulateApiDelay = (ms: number = 0): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

/**
 * Get all shipment alerts with optional filtering
 * 
 * @param filters - Optional search and filter parameters
 * @returns Promise<AlertsResponse> - Summary and alerts data
 */
export async function getShipmentAlerts(filters?: SearchFilters): Promise<AlertsResponse> {
  try {
    // No delay for static data
    await simulateApiDelay(0);

    let filteredAlerts = [...alertsData.alerts];

    // Apply filters if provided
    if (filters) {
      if (filters.query) {
        const query = filters.query.toLowerCase();
        filteredAlerts = filteredAlerts.filter(alert =>
          alert.title.toLowerCase().includes(query) ||
          alert.description.toLowerCase().includes(query) ||
          alert.shipmentId.toLowerCase().includes(query) ||
          alert.location.toLowerCase().includes(query)
        );
      }

      if (filters.type) {
        filteredAlerts = filteredAlerts.filter(alert => alert.type === filters.type);
      }

      if (filters.status) {
        filteredAlerts = filteredAlerts.filter(alert => alert.status === filters.status);
      }

      if (filters.priority) {
        filteredAlerts = filteredAlerts.filter(alert => alert.priority === filters.priority);
      }
    }

    // Recalculate summary based on filtered results
    const summary: AlertSummary = {
      totalAlerts: filteredAlerts.length,
      criticalAlerts: filteredAlerts.filter(a => a.type === 'critical').length,
      warningAlerts: filteredAlerts.filter(a => a.type === 'warning').length,
      infoAlerts: filteredAlerts.filter(a => a.type === 'info').length,
      resolvedToday: filteredAlerts.filter(a => a.status === 'resolved').length,
      pendingAlerts: filteredAlerts.filter(a => a.status === 'pending').length,
    };

    return {
      summary,
      alerts: filteredAlerts
    };
  } catch (error) {
    console.error('Error fetching shipment alerts:', error);
    throw new Error('Failed to fetch shipment alerts');
  }
}

/**
 * Get a specific alert by ID
 * 
 * @param alertId - The alert ID to fetch
 * @returns Promise<ShipmentAlert | null> - The alert data or null if not found
 */
export async function getAlertById(alertId: string): Promise<ShipmentAlert | null> {
  try {
    await simulateApiDelay(0);
    
    const alert = alertsData.alerts.find(a => a.id === alertId);
    return alert || null;
  } catch (error) {
    console.error('Error fetching alert by ID:', error);
    throw new Error('Failed to fetch alert');
  }
}

/**
 * Get summary statistics only
 * 
 * @returns Promise<AlertSummary> - Summary statistics
 */
export async function getAlertsSummary(): Promise<AlertSummary> {
  try {
    await simulateApiDelay(0);
    return alertsData.summary;
  } catch (error) {
    console.error('Error fetching alerts summary:', error);
    throw new Error('Failed to fetch alerts summary');
  }
}
