
### **Tracking System Frontend Layout using Next.js 15 + Tailwind CSS + UI ShadCN**

> Analyze the attached UI and extract a reusable layout template using **Next.js 15+ (App Router)** and **Tailwind CSS 4+**. Focus specifically on building:
>
> * A **two-part header** layout
> * A **responsive footer** with bilingual branding
> * **Language switcher** (Arabic ↔ English)
> * **User dropdown** and **agency logo**
> * **Main navigation menu bar**
>
> The design must fully support **RTL/LTR switching** for Arabic and English.

---

### 🧱 **Tech Stack Requirements**

* **Framework**: `Next.js 15` using `app/` directory (App Router)
* **Styling**: `Tailwind CSS` (latest) with RTL plugin support
* **Icons**: Use `Lucide`, `Heroicons`, or custom SVGs
* **Internationalization**: Use `next-intl` or `next-i18next`
* **Authentication**: Simulated user name with dropdown menu

---

### 🎨 **Header Layout**

#### 📌 **Top Header Section**

* Background: `bg-[#082d4f]`, height: `h-12`, text color: `text-white`
* **Flex layout**: `justify-between items-center px-6`
* **Left Side (LTR)**:

  * Language toggle: `English | Arabic`
  * Should update `dir` attribute (`ltr` or `rtl`)
* **Right Side (LTR)**:

  * Account dropdown showing user name (e.g., “Ahmed Ali”) with a chevron or down-arrow icon
  * Government logo block:

    * Arabic and English agency name stacked
    * Emblem positioned to the right (LTR) or left (RTL)
  * Responsive behavior: stack on mobile

#### 📌 **Bottom Navigation Bar**

* Background: `bg-white`, border-bottom: `border-b-2 border-blue-300`
* Height: `h-14`, padding: `px-6`
* **Horizontal navigation menu**:

  * Menu items: `Location Monitor`, `Focused Trips`, `My Assigned Ports` (active), `Dashboard`, `Configuration`, `Suspicious Trips`, `Reports ▼`
  * Active tab: `bg-[#007bff] text-white px-3 py-2 rounded-md`
  * Use `flex space-x-6` for LTR and `space-x-reverse` for RTL
  * Use `overflow-x-auto` for mobile responsiveness

---

### 🦶 **Footer Layout**

* Background: `bg-[#30B4B4]`, text color: `text-white`
* Layout: `flex justify-end items-center px-6 py-2`
* Content:

  * "Powered by" text
  * **TechnicalUnited** branding with English + Arabic
  * RTL mode should reverse alignment (use Tailwind’s `rtl:flex-row-reverse`)

---

### 🌍 **Internationalization Support**

* Apply `dir="ltr"` or `dir="rtl"` dynamically to `<html>` or `<body>`
* Use Tailwind’s `rtl:` and `ltr:` variants for conditional styling
* Language preference stored in cookie or URL prefix (`/en`, `/ar`)
* Language toggle should update locale and direction without full reload

---

### 📦 **Deliverables**

* `components/Header.tsx`

  * Contains:

    * Top bar (language + account + logo)
    * Navigation bar (menu items)
* `components/Footer.tsx`

  * Contains:

    * Branding and powered-by text
* `app/layout.tsx`

  * Wraps all pages with `<Header />` and `<Footer />`
  * Applies correct `dir` and `lang` attributes
* Tailwind config with RTL support

---

Would you like me to now generate the actual `Header.tsx` and `Footer.tsx` components using this design spec in Next.js 15 + Tailwind CSS?
