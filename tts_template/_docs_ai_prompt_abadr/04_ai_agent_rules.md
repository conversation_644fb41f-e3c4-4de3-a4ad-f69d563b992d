# Next.js 15+ & Tailwind 4+ & shadcn/ui Rules for AI Agents

## Next.js 15+ Architecture & Best Practices

### Project Structure (App Router)
```
app/
├── (auth)/           # Route groups for organization
│   ├── login/
│   └── register/
├── dashboard/
│   ├── page.tsx      # Server Component by default
│   └── loading.tsx   # Loading UI
├── api/
│   └── route.ts      # API routes
├── globals.css
├── layout.tsx        # Root layout
└── page.tsx          # Home page
```

### Key Changes in Next.js 15+
- **React 19 Support**: All components are updated for Tailwind v4 and React 19. We've removed the forwardRefs and adjusted the types
- **App Router is Default**: Use `app/` directory structure
- **Server Components by Default**: All components are server components unless marked with `'use client'`
- **Async Components**: Server components can be async functions
- **Turbopack**: Use `next dev --turbo` for faster development

### Component Patterns
```typescript
// Server Component (default)
export default async function Page() {
  const data = await fetch('...')
  return <div>{data}</div>
}

// Client Component
'use client'
import { useState } from 'react'

export default function ClientComponent() {
  const [state, setState] = useState()
  return <div>{state}</div>
}
```

### Data Fetching Best Practices
- **Use `fetch()` in Server Components**: Automatic caching and revalidation
- **Streaming with Suspense**: Wrap components with `<Suspense>` for better UX
- **Server Actions**: Use `'use server'` for form submissions and mutations
- **Parallel Data Fetching**: Use `Promise.all()` for multiple requests

```typescript
// Server Action
async function createUser(formData: FormData) {
  'use server'
  // Handle form submission
}

// In component
<form action={createUser}>
  <input name="name" />
  <button type="submit">Create</button>
</form>
```

### Performance Optimization
- **Dynamic Imports**: Use `next/dynamic` for code splitting
- **Image Optimization**: Always use `next/image` with proper sizing
- **Font Optimization**: Use `next/font` for font loading
- **Bundle Analysis**: Use `@next/bundle-analyzer`

## Tailwind CSS 4+ Key Changes

### New Engine & Performance
- Tailwind CSS v4.0 — an all-new version of the framework optimized for performance and flexibility, with a reimagined configuration and customization experience
- **Oxide Engine**: Significantly faster compilation
- **CSS-first Configuration**: Use CSS variables instead of JS config
- **Native CSS Features**: Better support for container queries, cascade layers

### Configuration Changes
```css
/* tailwind.config.css (replaces tailwind.config.js) */
@import "tailwindcss";

@theme {
  --color-brand: #3b82f6;
  --font-family-display: "Inter", system-ui, sans-serif;
}
```

### New Features
- **Container Queries**: `@container` support built-in
- **Cascade Layers**: Better CSS organization
- **CSS Variables**: Native CSS custom properties
- **Improved Dark Mode**: Better theme switching

### Migration Notes
- Configuration moved from JS to CSS
- Some utility classes may have changed
- Better performance with new engine
- Improved tree-shaking

## shadcn/ui Latest Updates

### Current State
- All components are updated for Tailwind v4 and React 19
- **No More forwardRef**: Components use React 19 features
- **Data Slots**: Every primitive now has a data-slot attribute for styling
- **Toast Deprecation**: We're deprecating the toast component in favor of sonner

### Installation & Setup
```bash
# Initialize shadcn/ui
npx shadcn@latest init

# Add components
npx shadcn@latest add button
npx shadcn@latest add card
```

### Component Usage Pattern
```typescript
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

export default function Component() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Title</CardTitle>
      </CardHeader>
      <CardContent>
        <Button>Click me</Button>
      </CardContent>
    </Card>
  )
}
```

### Styling with Data Slots
```css
/* Use data-slot for styling */
[data-slot="button"] {
  /* Custom button styles */
}
```

## AI Agent Rules & Guidelines

### 1. Next.js 15+ Architecture
- Use App Router (`app/` directory) not Pages Router
- Default to Server Components
- Use `'use client'` only when necessary
- Use Server Actions for form handling
- Keep Server Components async when fetching data
- Implement error boundaries with `error.tsx`
- Use `loading.tsx` for loading states

### 2. Tailwind CSS 4+ Rules
- Use CSS-first configuration instead of JS config
- Leverage Oxide Engine performance improvements
- Use `rtl:` and `ltr:` variants for directional styling
- Apply `dir` attribute dynamically to `<html>` or `<body>`
- Use data-slot attributes for component styling
- Implement proper responsive design with mobile-first approach

### 3. shadcn/ui Component Usage
- All components updated for React 19 (no forwardRef needed)
- Use data-slot attributes for custom styling
- Prefer sonner over deprecated toast component
- Import components from `@/components/ui/`
- Use TypeScript interfaces for component props

### 4. React 19+ Patterns
- Use new hooks and features
- Leverage improved Suspense behavior
- Remove unnecessary forwardRef usage
- Use proper TypeScript typing

### 5. Internationalization Best Practices
- Use `next-intl` or `next-i18next` for i18n
- Store language preference in cookies or URL
- Update locale and direction without full reload
- Use Tailwind RTL plugin for directional layouts

### 6. Performance Optimization
- Use `next/image` for all images
- Implement proper SEO with metadata API
- Use streaming and Suspense
- Optimize bundle size with dynamic imports
- Use Turbopack for development (`next dev --turbo`)

### 7. Error Handling Pattern
```typescript
// error.tsx
'use client'

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  return (
    <div>
      <h2>Something went wrong!</h2>
      <button onClick={reset}>Try again</button>
    </div>
  )
}
```

### 8. Testing Approach
- Use Vitest for unit tests
- Implement E2E tests with Playwright
- Test Server Components and Client Components separately
- Mock Server Actions in tests

### 9. Accessibility Standards
- Use semantic HTML
- Implement proper ARIA labels
- Ensure keyboard navigation
- Use shadcn/ui components for accessibility compliance

### 10. Code Organization
- Use barrel exports for cleaner imports
- Implement proper folder structure
- Use TypeScript strictly
- Follow consistent naming conventions
- Organize components by feature/domain

## Common Pitfalls to Avoid

1. **Don't use localStorage in artifacts** - Use React state instead
2. **Don't mix Server and Client Components improperly**
3. **Don't use outdated Tailwind config format**
4. **Don't forget to handle loading and error states**
5. **Don't ignore React 19 changes in shadcn/ui**

## Version Compatibility Matrix

| Tool | Version | Key Features |
|------|---------|-------------|
| Next.js | 15+ | React 19, Turbopack, App Router |
| Tailwind CSS | 4+ | Oxide Engine, CSS Config, Container Queries |
| shadcn/ui | Latest | No forwardRef, Data Slots, React 19 |
| React | 19+ | New hooks, Better Suspense, Compiler |

---

*Last Updated: July 2025*  
*Always check official documentation for the most current information*