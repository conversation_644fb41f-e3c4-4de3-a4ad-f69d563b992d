'use client';

import { useLanguage } from '../../contexts/LanguageContext';
import Link from 'next/link';
import { BarChart3, AlertTriangle, List, MapPin, Truck } from 'lucide-react';

export default function DemoPage() {
  const { t } = useLanguage();

  return (
    <div className="container mx-auto px-6 py-8">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-6">
          TTS Template - UI Components Demonstration
        </h1>

        {/* Quick Actions */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Quick Actions</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <Link
              href="/shipment-dashboard"
              className="group bg-white rounded-lg border-2 border-gray-200 p-6 hover:border-blue-300 hover:shadow-lg transition-all duration-200"
            >
              <div className="flex items-center space-x-4">
                <div className="bg-blue-100 p-3 rounded-lg group-hover:bg-blue-200 transition-colors">
                  <AlertTriangle className="w-8 h-8 text-blue-600" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-1">
                    Shipment Alerts Dashboard
                  </h3>
                  <p className="text-gray-600">
                    Monitor critical alerts, warnings, and track shipment incidents in real-time.
                  </p>
                </div>
              </div>
            </Link>

            <Link
              href="/list-demo"
              className="group bg-white rounded-lg border-2 border-gray-200 p-6 hover:border-green-300 hover:shadow-lg transition-all duration-200"
            >
              <div className="flex items-center space-x-4">
                <div className="bg-green-100 p-3 rounded-lg group-hover:bg-green-200 transition-colors">
                  <List className="w-8 h-8 text-green-600" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-1">
                    Master-Detail List Demo
                  </h3>
                  <p className="text-gray-600">
                    Demonstration of expandable table rows with detailed information views.
                  </p>
                </div>
              </div>
            </Link>

            <Link
              href="/demo-trip"
              className="group bg-white rounded-lg border-2 border-gray-200 p-6 hover:border-orange-300 hover:shadow-lg transition-all duration-200"
            >
              <div className="flex items-center space-x-4">
                <div className="bg-orange-100 p-3 rounded-lg group-hover:bg-orange-200 transition-colors">
                  <Truck className="w-8 h-8 text-orange-600" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-1">
                    Demo Trip Management
                  </h3>
                  <p className="text-gray-600">
                    Comprehensive trip tracking system with detailed trip information and status monitoring.
                  </p>
                </div>
              </div>
            </Link>

            <Link
              href="/map-demo"
              className="group bg-white rounded-lg border-2 border-gray-200 p-6 hover:border-purple-300 hover:shadow-lg transition-all duration-200"
            >
              <div className="flex items-center space-x-4">
                <div className="bg-purple-100 p-3 rounded-lg group-hover:bg-purple-200 transition-colors">
                  <MapPin className="w-8 h-8 text-purple-600" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-1">
                    Simple Maps Demo
                  </h3>
                  <p className="text-gray-600">
                    Beginner-friendly Google Maps integration with basic markers and interactions.
                  </p>
                </div>
              </div>
            </Link>

            <Link
              href="/map-demo2"
              className="group bg-white rounded-lg border-2 border-gray-200 p-6 hover:border-green-300 hover:shadow-lg transition-all duration-200"
            >
              <div className="flex items-center space-x-4">
                <div className="bg-green-100 p-3 rounded-lg group-hover:bg-green-200 transition-colors">
                  <MapPin className="w-8 h-8 text-green-600" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-1">
                    Location Monitor (Current Default)
                  </h3>
                  <p className="text-gray-600">
                    Full-screen Saudi Arabia map with trip monitoring sidebar, charts, and analytics.
                  </p>
                </div>
              </div>
            </Link>

            <div className="bg-white rounded-lg border-2 border-gray-200 p-6 opacity-75">
              <div className="flex items-center space-x-4">
                <div className="bg-gray-100 p-3 rounded-lg">
                  <BarChart3 className="w-8 h-8 text-gray-400" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-500 mb-1">
                    Analytics Dashboard
                  </h3>
                  <p className="text-gray-400">
                    Coming soon - Advanced analytics and reporting features.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
          <div className="bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-lg p-6">
            <div className="text-3xl font-bold mb-2">1,247</div>
            <div className="text-blue-100">Total Operations</div>
          </div>
          <div className="bg-gradient-to-r from-green-500 to-green-600 text-white rounded-lg p-6">
            <div className="text-3xl font-bold mb-2">98.5%</div>
            <div className="text-green-100">System Uptime</div>
          </div>
          <div className="bg-gradient-to-r from-yellow-500 to-yellow-600 text-white rounded-lg p-6">
            <div className="text-3xl font-bold mb-2">24</div>
            <div className="text-yellow-100">Active Alerts</div>
          </div>
          <div className="bg-gradient-to-r from-purple-500 to-purple-600 text-white rounded-lg p-6">
            <div className="text-3xl font-bold mb-2">156</div>
            <div className="text-purple-100">Users Online</div>
          </div>
        </div>

        {/* Configuration Categories */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          <div className="bg-white border rounded-lg p-6 hover:shadow-md transition-shadow">
            <div className="flex items-center mb-4">
              <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold">System Settings</h3>
            </div>
            <p className="text-gray-600 text-sm mb-4">
              Configure core system parameters and operational settings.
            </p>
            <button className="text-blue-600 text-sm font-medium hover:text-blue-800">
              Manage Settings →
            </button>
          </div>

          <div className="bg-white border rounded-lg p-6 hover:shadow-md transition-shadow">
            <div className="flex items-center mb-4">
              <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold">User Management</h3>
            </div>
            <p className="text-gray-600 text-sm mb-4">
              Manage user accounts, roles, and access permissions.
            </p>
            <button className="text-blue-600 text-sm font-medium hover:text-blue-800">
              Manage Users →
            </button>
          </div>

          <div className="bg-white border rounded-lg p-6 hover:shadow-md transition-shadow">
            <div className="flex items-center mb-4">
              <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                <svg className="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold">Security</h3>
            </div>
            <p className="text-gray-600 text-sm mb-4">
              Configure security policies and authentication settings.
            </p>
            <button className="text-blue-600 text-sm font-medium hover:text-blue-800">
              Security Settings →
            </button>
          </div>
        </div>

        {/* Port Status Cards */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          <div className="bg-white border rounded-lg p-6">
            <h3 className="text-lg font-semibold mb-4">Port Alpha</h3>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">Status:</span>
                <span className="text-green-600 font-medium">Operational</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Vessels:</span>
                <span className="font-medium">12 Active</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Capacity:</span>
                <span className="font-medium">85%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div className="bg-green-500 h-2 rounded-full" style={{width: '85%'}}></div>
              </div>
            </div>
          </div>

          <div className="bg-white border rounded-lg p-6">
            <h3 className="text-lg font-semibold mb-4">Port Delta</h3>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">Status:</span>
                <span className="text-red-600 font-medium">Alert</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Vessels:</span>
                <span className="font-medium">15 Active</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Capacity:</span>
                <span className="font-medium">95%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div className="bg-red-500 h-2 rounded-full" style={{width: '95%'}}></div>
              </div>
            </div>
          </div>
        </div>

        {/* Features Overview */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">
            TTS Template Features
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="font-medium text-gray-800 mb-2">Technical Features:</h3>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Next.js 15+ with App Router</li>
                <li>• Tailwind CSS 4+ with RTL support</li>
                <li>• shadcn/ui components</li>
                <li>• React Context internationalization</li>
                <li>• Mobile responsive navigation</li>
                <li>• Language switching (Arabic/English)</li>
              </ul>
            </div>

            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="font-medium text-gray-800 mb-2">Navigation Pages:</h3>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• {t('navigation.locationMonitor')}</li>
                <li>• {t('navigation.focusedTrips')}</li>
                <li>• {t('navigation.myAssignedPorts')}</li>
                <li>• {t('navigation.dashboard')}</li>
                <li>• {t('navigation.configuration')}</li>
                <li>• {t('navigation.suspiciousTrips')}</li>
                <li>• {t('navigation.reports')}</li>
              </ul>
            </div>
          </div>
        </div>

        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <h3 className="text-lg font-medium text-blue-900 mb-2">
            Language & Direction Testing
          </h3>
          <p className="text-blue-800">
            Use the language switcher in the top header to test RTL/LTR functionality.
            The layout will automatically adjust direction and text alignment based on the selected language.
            All navigation pages show only titles for clean testing.
          </p>
        </div>
      </div>
    </div>
  );
}
