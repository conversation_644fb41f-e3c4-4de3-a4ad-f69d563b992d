/**
 * Shipment Alerts Dashboard Page
 * 
 * This page demonstrates how to create a new page with JSON API integration.
 * It includes:
 * - Summary cards with statistics
 * - Search and filtering functionality
 * - Alerts list with detailed information
 * - Loading states and error handling
 * 
 * To add a new page like this:
 * 1. Create a new folder in /app with your page name
 * 2. Add a page.tsx file (this file)
 * 3. Create your data structure in /data/*.json
 * 4. Create API functions in /api/*.ts
 * 5. Create reusable components in /components
 * 6. Import and use the API functions with React hooks
 */

'use client';

import React, { useState, useEffect } from 'react';
import { useLanguage } from '../../contexts/LanguageContext';
import SummaryCard from '../../components/SummaryCard';
import SearchPanel from '../../components/SearchPanel';
import AlertsList from '../../components/AlertsList';
import AlertsTable from '../../components/AlertsTable';
import ViewToggle from '../../components/ViewToggle';
import { 
  getShipmentAlerts, 
  type AlertsResponse, 
  type SearchFilters 
} from '../../api/shipment-alerts';
import { 
  AlertTriangle, 
  AlertCircle, 
  Info, 
  CheckCircle, 
  Clock, 
  TrendingUp 
} from 'lucide-react';

export default function ShipmentDashboard() {
  const { t } = useLanguage();
  const [data, setData] = useState<AlertsResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [view, setView] = useState<'table' | 'list'>('list');

  // Load initial data
  useEffect(() => {
    loadAlerts();
  }, []);

  const loadAlerts = async (filters?: SearchFilters) => {
    try {
      setLoading(true);
      setError(null);
      const response = await getShipmentAlerts(filters);
      setData(response);
    } catch (err) {
      setError('Failed to load shipment alerts. Please try again.');
      console.error('Error loading alerts:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (filters: SearchFilters) => {
    loadAlerts(filters);
  };

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="bg-white rounded-lg shadow-lg p-8 max-w-md w-full mx-4">
          <div className="text-center">
            <AlertTriangle className="w-12 h-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Error Loading Dashboard</h2>
            <p className="text-gray-600 mb-4">{error}</p>
            <button
              onClick={() => loadAlerts()}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Shipment Alerts Dashboard
          </h1>
          <p className="text-gray-600">
            Monitor and track shipment alerts, delays, and security incidents in real-time.
          </p>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6 mb-8">
          <SummaryCard
            label="Total Alerts"
            count={data?.summary.totalAlerts || 0}
            icon={TrendingUp}
            variant="default"
          />
          <SummaryCard
            label="Critical Alerts"
            count={data?.summary.criticalAlerts || 0}
            icon={AlertTriangle}
            variant="critical"
          />
          <SummaryCard
            label="Warning Alerts"
            count={data?.summary.warningAlerts || 0}
            icon={AlertCircle}
            variant="warning"
          />
          <SummaryCard
            label="Resolved Today"
            count={data?.summary.resolvedToday || 0}
            icon={CheckCircle}
            variant="success"
          />
          <SummaryCard
            label="Pending Alerts"
            count={data?.summary.pendingAlerts || 0}
            icon={Clock}
            variant="warning"
          />
        </div>

        {/* Search Panel */}
        <SearchPanel onSearch={handleSearch} loading={loading} />

        {/* Alerts List */}
        <div>
          <div className="flex items-center justify-between mb-6">
            <div>
              <h2 className="text-xl font-semibold text-gray-900">
                Recent Alerts
              </h2>
              <div className="text-sm text-gray-500 mt-1">
                {data?.alerts.length || 0} alerts found
              </div>
            </div>

            <ViewToggle view={view} onViewChange={setView} />
          </div>

          {view === 'list' ? (
            <AlertsList alerts={data?.alerts || []} loading={loading} />
          ) : (
            <AlertsTable alerts={data?.alerts || []} loading={loading} />
          )}
        </div>
      </div>
    </div>
  );
}
