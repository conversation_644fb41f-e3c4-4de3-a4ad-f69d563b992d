# Translation Implementation Guide

## Overview

The TTS Template uses a modular translation system built with React Context and separate translation files. This approach provides type safety, easy maintenance, and seamless language switching without URL complexity.

## Architecture

### Translation System Components

1. **Translation Files** (`/translations/`)
   - Separate files for each language
   - TypeScript exports for type safety
   - Centralized index file for exports

2. **Language Context** (`/contexts/LanguageContext.tsx`)
   - React Context for state management
   - Translation function (`t()`)
   - Language switching logic
   - Persistent storage

3. **Component Integration**
   - `useLanguage` hook for components
   - Automatic RTL/LTR direction switching
   - Real-time UI updates

## File Structure

```
translations/
├── index.ts        # Main exports and TypeScript types
├── en.ts          # English translations
└── ar.ts          # Arabic translations
```

### Translation File Format

Each language file exports a translation object:

```typescript
// translations/en.ts
export const en = {
  'header.language': 'English',
  'header.userAccount': 'Ahmed Ali',
  'navigation.dashboard': 'Dashboard',
  'page.title': 'Page Title'
} as const;
```

### Index File Structure

```typescript
// translations/index.ts
import { en } from './en';
import { ar } from './ar';

export type Language = 'en' | 'ar';
export type TranslationKey = keyof typeof en;

export const translations = { en, ar } as const;
export { en, ar };
```

## Adding Translations to New Pages

### Step 1: Add Translation Keys

Add your new translation keys to all language files:

```typescript
// translations/en.ts
export const en = {
  // Existing translations...
  'newPage.title': 'New Page Title',
  'newPage.description': 'Page description',
  'newPage.button': 'Click Here'
} as const;

// translations/ar.ts
export const ar = {
  // Existing translations...
  'newPage.title': 'عنوان الصفحة الجديدة',
  'newPage.description': 'وصف الصفحة',
  'newPage.button': 'انقر هنا'
} as const;
```

### Step 2: Use in Components

Import and use the `useLanguage` hook in your components:

```typescript
// app/new-page/page.tsx
'use client';

import { useLanguage } from '../../contexts/LanguageContext';

export default function NewPage() {
  const { t, language, dir } = useLanguage();

  return (
    <div dir={dir}>
      <h1>{t('newPage.title')}</h1>
      <p>{t('newPage.description')}</p>
      <button>{t('newPage.button')}</button>
    </div>
  );
}
```

### Step 3: Test Both Languages

1. Run the development server
2. Navigate to your new page
3. Switch languages using the globe icon
4. Verify all text updates correctly
5. Check RTL/LTR layout changes

## Translation Key Naming Conventions

### Hierarchical Structure
Use dot notation for logical grouping:

```typescript
{
  'header.userAccount': 'User Account',
  'header.language': 'Language',
  'navigation.dashboard': 'Dashboard',
  'navigation.reports': 'Reports',
  'page.home.title': 'Home Page',
  'page.home.welcome': 'Welcome Message',
  'form.submit': 'Submit',
  'form.cancel': 'Cancel'
}
```

### Naming Guidelines

1. **Prefix by Section**: `header.`, `navigation.`, `page.`, `form.`
2. **Use Descriptive Names**: `userAccount` not `user`
3. **Be Consistent**: Same pattern across all languages
4. **Avoid Abbreviations**: `dashboard` not `dash`

## Best Practices

### Translation Management

1. **Keep Keys Synchronized**: Ensure all language files have the same keys
2. **Use TypeScript**: Leverage type checking for missing translations
3. **Consistent Formatting**: Maintain same structure across files
4. **Regular Reviews**: Check translations for accuracy and context

### Component Usage

1. **Extract All Text**: Don't hardcode any user-facing text
2. **Use Semantic Keys**: Keys should describe content, not appearance
3. **Handle Pluralization**: Consider different plural rules for languages
4. **Context Awareness**: Provide context for translators when needed

### Performance Considerations

1. **Lazy Loading**: Consider lazy loading for large translation files
2. **Bundle Size**: Monitor translation file sizes
3. **Caching**: Leverage browser caching for translation files
4. **Tree Shaking**: Ensure unused translations are removed

## Advanced Features

### Dynamic Translations

For dynamic content, use template strings:

```typescript
// Translation
'message.welcome': 'Welcome, {name}!'

// Usage
const welcomeMessage = t('message.welcome').replace('{name}', userName);
```

### Conditional Content

Handle language-specific content:

```typescript
const { language } = useLanguage();

return (
  <div>
    {language === 'ar' ? (
      <ArabicSpecificComponent />
    ) : (
      <EnglishSpecificComponent />
    )}
  </div>
);
```

### RTL-Aware Styling

Use direction-aware classes:

```typescript
<div className={`flex ${dir === 'rtl' ? 'flex-row-reverse' : 'flex-row'}`}>
  <span className="rtl:text-right ltr:text-left">
    {t('content.text')}
  </span>
</div>
```

## Migration from Inline Translations

If you have existing hardcoded text:

### Before
```typescript
<h1>Dashboard</h1>
<p>Welcome to the system</p>
```

### After
```typescript
<h1>{t('page.dashboard.title')}</h1>
<p>{t('page.dashboard.welcome')}</p>
```

## Troubleshooting

### Common Issues

1. **Missing Translation Key**: Check TypeScript errors for typos
2. **Key Not Found**: Verify key exists in all language files
3. **RTL Layout Issues**: Test with Arabic content and verify CSS
4. **Context Not Available**: Ensure component is wrapped in LanguageProvider

### Debugging Tips

1. **Console Logging**: Add logs to translation function
2. **Browser DevTools**: Check localStorage for language preference
3. **Type Checking**: Use TypeScript to catch missing keys
4. **Visual Testing**: Test both languages in browser

## Future Enhancements

### Potential Improvements

1. **Translation Management UI**: Admin interface for managing translations
2. **Pluralization Support**: Handle complex plural rules
3. **Interpolation**: Advanced template string support
4. **Namespace Support**: Organize translations by feature
5. **External Services**: Integration with translation services
6. **Validation**: Automated checks for missing translations

This translation system provides a solid foundation for bilingual applications while maintaining simplicity and type safety.
