# TTS Template - Project Overview

## Project Description

This is a Trip Tracking System (TTS) template built with Next.js 15+ that provides a bilingual (Arabic/English) government portal interface. The template includes a responsive header with navigation, language switching functionality, proper RTL/LTR support, and a comprehensive shipment alerts dashboard with API integration patterns.

## Technology Stack

### Core Technologies

- **Next.js 15.3.5** - React framework with App Router
- **React 19** - UI library
- **TypeScript** - Type safety
- **Tailwind CSS 4+** - Styling with RTL support
- **shadcn/ui** - UI component library

### Key Dependencies

- `lucide-react` - Icons
- Custom React Context for internationalization

## Architecture Decisions

### 1. Internationalization Approach

**Decision**: Use React Context instead of next-intl with URL-based routing
**Reasoning**:

- Simpler implementation without URL complexity
- Better user experience (no URL changes)
- Easier to maintain and debug
- Persistent language preference via localStorage

### 2. Styling Strategy

**Decision**: Tailwind CSS 4+ with manual RTL classes
**Reasoning**:

- Latest Tailwind features
- Better performance with CSS-first approach
- Custom RTL utilities for precise control
- Consistent design system

### 3. Component Structure

**Decision**: Modular component architecture with shared UI components
**Reasoning**:

- Reusable components
- Easy maintenance
- Consistent styling
- shadcn/ui integration

## Project Structure

```
poc_apps/tts_template/
├── app/
│   ├── layout.tsx              # Root layout with providers
│   ├── page.tsx                # Home page with dashboard links
│   ├── shipment-dashboard/     # Shipment alerts dashboard
│   │   └── page.tsx            # Dashboard page component
│   └── globals.css             # Global styles and Tailwind config
├── components/
│   ├── Header.tsx              # Main header component
│   ├── Footer.tsx              # Footer component
│   ├── Breadcrumb.tsx          # Navigation breadcrumbs
│   ├── LanguageSwitcher.tsx    # Language toggle component
│   ├── SummaryCard.tsx         # Reusable summary cards
│   ├── AlertsList.tsx          # List view for alerts
│   ├── AlertsTable.tsx         # Table view for alerts
│   ├── SearchPanel.tsx         # Search and filtering component
│   ├── ViewToggle.tsx          # Table/List view toggle
│   └── ui/                     # shadcn/ui components
├── contexts/
│   └── LanguageContext.tsx     # Language management context
├── translations/
│   ├── index.ts                # Translation exports and types
│   ├── en.ts                   # English translations
│   └── ar.ts                   # Arabic translations
├── api/
│   └── shipment-alerts.ts      # API layer for alerts data
├── data/
│   └── shipment-alerts.json    # Static JSON data source
└── _docs_implementation/       # Documentation
```

## Features Implemented

### ✅ Core Infrastructure

1. **Responsive Header System**

   - Two-tier header (top bar + navigation)
   - Language switcher with globe icon
   - User account dropdown
   - Government agency branding
   - Navigation menu with active states
2. **Footer & Branding**

   - Fixed footer with global branding colors
   - Bilingual content support
   - Responsive design
3. **Internationalization System**

   - Modular translation files (`/translations/`)
   - Arabic/English language switching
   - RTL/LTR layout support
   - Persistent language preference
   - Dynamic text direction

### ✅ Dashboard Features

4. **Shipment Alerts Dashboard**

   - Comprehensive alerts tracking interface
   - Real-time status monitoring
   - Professional government portal design
5. **Data Visualization Components**

   - Summary cards with icons and metrics
   - Color-coded status indicators
   - Responsive grid layouts
6. **Data Views & Interaction**

   - Table view with sortable columns
   - List view with detailed cards
   - View toggle functionality
   - Advanced search and filtering

### ✅ API Integration Pattern

7. **Static JSON API Layer**
   - HTTP-like API functions
   - TypeScript interfaces
   - Easy migration path to real APIs
   - Simulated realistic data flow

### ✅ UI Components & Styling

8. **Component Library**

   - shadcn/ui integration
   - Custom reusable components
   - Consistent design system
   - Professional government styling
9. **Advanced Styling**

   - Tailwind CSS 4+ configuration
   - Global branding color variables
   - Custom RTL utilities
   - Responsive breakpoints

### 🎯 Key Achievements

**Core Infrastructure:**

- ✅ Next.js 15+ with App Router
- ✅ Tailwind CSS 4+ with RTL support
- ✅ Modular translation system
- ✅ Bilingual interface (Arabic/English)
- ✅ Responsive design across all devices

**Dashboard & Data Features:**

- ✅ Complete shipment alerts dashboard
- ✅ Table/List view toggle functionality
- ✅ Advanced search and filtering
- ✅ Professional summary cards
- ✅ Static JSON API layer with migration path

**UI & Design:**

- ✅ shadcn/ui components integration
- ✅ Global branding color system
- ✅ Government portal styling
- ✅ Professional data visualization

**Developer Experience:**

- ✅ TypeScript type safety
- ✅ Comprehensive documentation
- ✅ Clear patterns for extension
- ✅ No URL-based routing complexity

## Testing Results

**Core Functionality:**

- ✅ Development server runs successfully
- ✅ All pages load without errors (200 status)
- ✅ Components render correctly
- ✅ Language switching works seamlessly
- ✅ RTL/LTR layout changes properly
- ✅ Responsive design functions across devices


**Navigation & UI:**

- ✅ Navigation structure displays properly
- ✅ Breadcrumbs work correctly
- ✅ Mobile navigation functions
- ✅ Footer displays with correct branding
- ✅ All interactive elements respond properly
