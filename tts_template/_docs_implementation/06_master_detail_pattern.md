# Master-Detail Pattern Implementation Guide

## Overview

The Master-Detail pattern is a fundamental UI design pattern that displays a list of items (master view) alongside detailed information about a selected item (detail view). In the TTS Template, this pattern is demonstrated through expandable table rows that reveal additional information inline.

## Pattern Benefits

### User Experience
- **Efficient Information Display**: Shows essential data in compact format with details on demand
- **Reduced Navigation**: Users don't need to navigate to separate pages for details
- **Context Preservation**: Maintains list context while viewing details
- **Progressive Disclosure**: Reveals information progressively based on user interest

### Technical Advantages
- **Performance**: Only loads detailed data when requested
- **Scalability**: Handles large datasets efficiently
- **Responsive**: Works well on both desktop and mobile devices
- **Accessibility**: Supports keyboard navigation and screen readers

## Implementation Architecture

### Component Structure

```
ListDemo Page
├── Table Container
│   ├── Table Header (5 columns)
│   └── Table Body
│       ├── Master Row (compact view)
│       └── Detail Row (expandable section)
└── State Management (expandedRows)
```

### Data Flow

1. **Initial Load**: Fetch data from API layer
2. **Master Display**: Show essential information in table rows
3. **User Interaction**: Click expand icon to toggle detail view
4. **Detail Rendering**: Render additional information inline
5. **State Management**: Track which rows are expanded

## Technical Implementation

### State Management Pattern

```typescript
// Track expanded rows using Set for efficient operations
const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set());

// Toggle expansion with immutable state updates
const toggleRowExpansion = (alertId: string) => {
  const newExpandedRows = new Set(expandedRows);
  if (newExpandedRows.has(alertId)) {
    newExpandedRows.delete(alertId);
  } else {
    newExpandedRows.add(alertId);
  }
  setExpandedRows(newExpandedRows);
};
```

### Table Structure Design

#### Master Row Layout
```typescript
// 5-column structure for optimal information density
1. Icon Column (w-12): Expand/collapse button
2. Alert Column: Primary information with icon and description
3. Status Column: Color-coded status badges
4. Priority Column: Priority level indicators
5. Time Column: Formatted timestamps
```

#### Detail Row Implementation
```typescript
// Expandable section spans all columns
<tr className="bg-gray-50">
  <td colSpan={5} className="px-6 py-4">
    <div className="animate-in slide-in-from-top-2 duration-200">
      {/* Detailed information grid */}
    </div>
  </td>
</tr>
```

### Animation System

#### CSS Animation Classes
```css
.animate-in {
  animation-duration: 200ms;
  animation-fill-mode: both;
}

.slide-in-from-top-2 {
  animation-name: slideInFromTop;
}

@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
```

## UI/UX Design Principles

### Visual Hierarchy

#### Master View Information Priority
1. **Primary**: Alert title and type icon
2. **Secondary**: Status and priority badges
3. **Tertiary**: Timestamp and truncated description

#### Detail View Information Organization
1. **Full Description**: Complete alert information
2. **Metadata Grid**: Shipment ID, location, assigned user
3. **Timestamps**: Detailed creation information

### Interaction Design

#### Icon Button Specifications
- **Size**: 32x32px for optimal touch targets
- **Position**: First column for consistent interaction
- **States**: Collapsed (►) and expanded (▼) chevrons
- **Feedback**: Hover effects and focus rings

#### Accessibility Features
- **ARIA Labels**: `aria-expanded` for screen readers
- **Keyboard Support**: Tab navigation and Enter/Space activation
- **Color Independence**: Icons work without color perception
- **Semantic HTML**: Proper button and table semantics

## Responsive Design Strategy

### Desktop Experience (≥768px)
- **Full Table**: All columns visible
- **Hover Effects**: Interactive feedback on buttons
- **Optimal Spacing**: Comfortable padding and margins

### Tablet Experience (768px-1024px)
- **Horizontal Scroll**: Table scrolls horizontally if needed
- **Touch Targets**: Buttons sized for finger interaction
- **Readable Text**: Appropriate font sizes maintained

### Mobile Experience (<768px)
- **Compact Layout**: Essential information prioritized
- **Touch-Friendly**: Large enough touch targets
- **Vertical Stacking**: Detail grid adapts to single column

## Data Presentation Patterns

### Master View Data Strategy
```typescript
// Show essential information only
- Alert type icon and title
- Truncated description (60 characters)
- Status and priority badges
- Formatted timestamp
```

### Detail View Data Strategy
```typescript
// Comprehensive information display
- Full description text
- Complete metadata (shipment ID, location, assignee)
- Detailed timestamps
- Structured grid layout (1/2/3 columns based on screen size)
```

## Performance Considerations

### Efficient Rendering
- **Conditional Rendering**: Detail sections only render when expanded
- **State Optimization**: Set-based state for O(1) lookup operations
- **Animation Performance**: CSS animations for smooth transitions

### Memory Management
- **Lazy Detail Loading**: Could be extended to load details on demand
- **State Cleanup**: Proper state management prevents memory leaks
- **Component Optimization**: React.Fragment reduces DOM nodes

## Accessibility Implementation

### Screen Reader Support
```typescript
// Proper ARIA attributes
aria-expanded={isExpanded}
aria-label={isExpanded ? 'Collapse details' : 'Expand details'}
```

### Keyboard Navigation
- **Tab Order**: Logical tab sequence through expand buttons
- **Activation**: Enter and Space keys trigger expansion
- **Focus Management**: Visible focus indicators

### Color and Contrast
- **Icon Clarity**: Chevron icons work without color
- **Contrast Ratios**: Meet WCAG guidelines
- **Status Indicators**: Icons supplement color coding

## Extension Patterns

### Multiple Selection Support
```typescript
// Allow multiple rows expanded simultaneously
const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set());

// Or implement accordion behavior (single row)
const [expandedRow, setExpandedRow] = useState<string | null>(null);
```

### Dynamic Detail Loading
```typescript
// Load details on demand for performance
const [detailData, setDetailData] = useState<Map<string, DetailData>>(new Map());

const loadDetails = async (alertId: string) => {
  if (!detailData.has(alertId)) {
    const details = await fetchAlertDetails(alertId);
    setDetailData(prev => new Map(prev).set(alertId, details));
  }
};
```

### Custom Detail Templates
```typescript
// Different detail layouts based on data type
const renderDetailContent = (alert: ShipmentAlert) => {
  switch (alert.type) {
    case 'critical':
      return <CriticalAlertDetails alert={alert} />;
    case 'warning':
      return <WarningAlertDetails alert={alert} />;
    default:
      return <StandardAlertDetails alert={alert} />;
  }
};
```

## Best Practices

### State Management
1. **Immutable Updates**: Always create new state objects
2. **Efficient Data Structures**: Use Set for O(1) operations
3. **Clear State**: Reset expanded state when data changes
4. **Persistent State**: Consider localStorage for user preferences

### Performance Optimization
1. **Conditional Rendering**: Only render expanded content when needed
2. **Animation Timing**: Keep animations under 300ms
3. **Event Handling**: Debounce rapid clicks if necessary
4. **Memory Cleanup**: Clear unused state and event listeners

### User Experience
1. **Visual Feedback**: Provide immediate response to interactions
2. **Consistent Behavior**: Same interaction pattern throughout
3. **Error Handling**: Graceful degradation when data fails to load
4. **Loading States**: Show appropriate loading indicators

## Testing Strategies

### Functional Testing
- **Expansion/Collapse**: Verify all rows can be toggled
- **State Persistence**: Check state maintains during re-renders
- **Data Integrity**: Ensure correct data displays in details
- **Error Handling**: Test behavior with missing or invalid data

### Accessibility Testing
- **Screen Reader**: Test with NVDA, JAWS, or VoiceOver
- **Keyboard Navigation**: Verify tab order and activation
- **Color Blindness**: Test with color vision simulators
- **High Contrast**: Verify visibility in high contrast mode

### Performance Testing
- **Large Datasets**: Test with hundreds of rows
- **Animation Performance**: Monitor frame rates during transitions
- **Memory Usage**: Check for memory leaks with repeated interactions
- **Mobile Performance**: Test on actual mobile devices

This master-detail pattern provides a robust foundation for displaying hierarchical data with excellent user experience and technical performance.
