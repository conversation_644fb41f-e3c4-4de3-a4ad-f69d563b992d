# Beginner's Guide to Google Maps in Next.js

## Overview

This guide provides a step-by-step tutorial for fresh developers to integrate Google Maps into a Next.js application. We'll start with the basics and build up to a working map with interactive markers.

## Prerequisites

### What You Need to Know
- Basic React/Next.js knowledge
- Understanding of React hooks (useState, useEffect)
- Basic TypeScript (optional but recommended)

### What You Need to Have
- A Google Cloud Platform account
- A Google Maps API key
- A Next.js project set up

## Step 1: Get Your Google Maps API Key

### 1.1 Create Google Cloud Project
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Click "Create Project" or select an existing project
3. Give your project a name (e.g., "My Maps App")

### 1.2 Enable Maps JavaScript API
1. In the Google Cloud Console, go to "APIs & Services" > "Library"
2. Search for "Maps JavaScript API"
3. Click on it and press "Enable"

### 1.3 Create API Key
1. Go to "APIs & Services" > "Credentials"
2. Click "Create Credentials" > "API Key"
3. Copy the generated API key
4. (Optional) Click "Restrict Key" to add security restrictions

### 1.4 Add API Key to Your Project
Create a `.env.local` file in your project root:

```bash
# .env.local
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=your_api_key_here
```

**Important**: Never commit this file to version control!

## Step 2: Create the Google Maps Utility

Create a utility file to handle Google Maps API loading:

```typescript
// utils/googleMaps.ts
let loadPromise: Promise<void> | null = null;

export function loadGoogleMapsAPI(): Promise<void> {
  // Return existing promise if already loading
  if (loadPromise) {
    return loadPromise;
  }

  // Return resolved promise if already loaded
  if (window.google && window.google.maps) {
    return Promise.resolve();
  }

  // Create new loading promise
  loadPromise = new Promise((resolve, reject) => {
    const script = document.createElement('script');
    const apiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY;
    
    if (!apiKey) {
      reject(new Error('Google Maps API key is not configured'));
      return;
    }

    script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}`;
    script.async = true;
    script.defer = true;
    
    script.onload = () => resolve();
    script.onerror = () => reject(new Error('Failed to load Google Maps API'));

    document.head.appendChild(script);
  });

  return loadPromise;
}
```

## Step 3: Create Your Map Component

### 3.1 Basic Component Structure

```typescript
// app/map-demo/page.tsx
'use client';

import React, { useState, useEffect, useRef } from 'react';
import { loadGoogleMapsAPI } from '../../utils/googleMaps';

export default function MapDemo() {
  // Step 1: Set up component state
  const mapRef = useRef<HTMLDivElement>(null);
  const [map, setMap] = useState<google.maps.Map | null>(null);
  const [isLoaded, setIsLoaded] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // More code will go here...
}
```

### 3.2 Load Google Maps API

```typescript
// Step 2: Load Google Maps API
useEffect(() => {
  loadGoogleMapsAPI()
    .then(() => {
      setIsLoaded(true);
      setError(null);
    })
    .catch((err) => {
      setError(err.message);
      console.error('Error loading Google Maps API:', err);
    });
}, []);
```

### 3.3 Initialize the Map

```typescript
// Step 3: Initialize the map
useEffect(() => {
  if (!isLoaded || !mapRef.current) return;

  const mapInstance = new google.maps.Map(mapRef.current, {
    center: { lat: 24.7136, lng: 46.6753 }, // Riyadh center
    zoom: 11,
  });

  setMap(mapInstance);
}, [isLoaded]);
```

### 3.4 Add Markers

```typescript
// Sample marker data
const sampleMarkers = [
  {
    id: '1',
    name: 'Location 1',
    lat: 24.7136,
    lng: 46.6753,
    address: 'Address 1'
  },
  {
    id: '2',
    name: 'Location 2',
    lat: 24.6877,
    lng: 46.6947,
    address: 'Address 2'
  }
];

// Step 4: Add markers to the map
useEffect(() => {
  if (!map) return;

  const markers = sampleMarkers.map(markerData => {
    const marker = new google.maps.Marker({
      position: { lat: markerData.lat, lng: markerData.lng },
      map: map,
      title: markerData.name,
    });

    // Add click event
    marker.addListener('click', () => {
      alert(`You clicked on ${markerData.name}`);
    });

    return marker;
  });

  // Cleanup function
  return () => {
    markers.forEach(marker => marker.setMap(null));
  };
}, [map]);
```

### 3.5 Handle Loading and Error States

```typescript
// Step 5: Handle loading and error states
if (error) {
  return (
    <div className="p-8 text-center">
      <h3 className="text-lg font-medium text-red-600 mb-2">Error Loading Map</h3>
      <p className="text-gray-500">{error}</p>
    </div>
  );
}

if (!isLoaded) {
  return (
    <div className="p-8 text-center">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
      <p className="text-gray-500">Loading Google Maps...</p>
    </div>
  );
}
```

### 3.6 Render the Map

```typescript
// Step 6: Render the map component
return (
  <div className="container mx-auto px-6 py-8">
    <h1 className="text-3xl font-bold text-gray-900 mb-4">
      My Google Map
    </h1>
    
    <div className="bg-white rounded-lg border overflow-hidden">
      <div 
        ref={mapRef} 
        className="w-full h-96"
        style={{ minHeight: '400px' }}
      />
    </div>
  </div>
);
```

## Step 4: Complete Example

Here's the complete component code:

```typescript
'use client';

import React, { useState, useEffect, useRef } from 'react';
import { loadGoogleMapsAPI } from '../../utils/googleMaps';

const sampleMarkers = [
  { id: '1', name: 'Location 1', lat: 24.7136, lng: 46.6753, address: 'Address 1' },
  { id: '2', name: 'Location 2', lat: 24.6877, lng: 46.6947, address: 'Address 2' }
];

export default function MapDemo() {
  const mapRef = useRef<HTMLDivElement>(null);
  const [map, setMap] = useState<google.maps.Map | null>(null);
  const [isLoaded, setIsLoaded] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load Google Maps API
  useEffect(() => {
    loadGoogleMapsAPI()
      .then(() => setIsLoaded(true))
      .catch((err) => setError(err.message));
  }, []);

  // Initialize map
  useEffect(() => {
    if (!isLoaded || !mapRef.current) return;

    const mapInstance = new google.maps.Map(mapRef.current, {
      center: { lat: 24.7136, lng: 46.6753 },
      zoom: 11,
    });

    setMap(mapInstance);
  }, [isLoaded]);

  // Add markers
  useEffect(() => {
    if (!map) return;

    const markers = sampleMarkers.map(markerData => {
      const marker = new google.maps.Marker({
        position: { lat: markerData.lat, lng: markerData.lng },
        map: map,
        title: markerData.name,
      });

      marker.addListener('click', () => {
        alert(`You clicked on ${markerData.name}`);
      });

      return marker;
    });

    return () => markers.forEach(marker => marker.setMap(null));
  }, [map]);

  if (error) {
    return <div className="p-8 text-center text-red-600">{error}</div>;
  }

  if (!isLoaded) {
    return <div className="p-8 text-center">Loading Google Maps...</div>;
  }

  return (
    <div className="container mx-auto px-6 py-8">
      <h1 className="text-3xl font-bold mb-4">My Google Map</h1>
      <div className="bg-white rounded-lg border overflow-hidden">
        <div ref={mapRef} className="w-full h-96" />
      </div>
    </div>
  );
}
```

## Common Issues and Solutions

### Issue 1: "API key not configured"
**Solution**: Make sure your `.env.local` file exists and contains the correct API key.

### Issue 2: "Failed to load Google Maps API"
**Solution**: Check your internet connection and verify your API key is valid.

### Issue 3: Map not showing
**Solution**: Ensure the map container has a defined height (use `h-96` or `style={{ height: '400px' }}`).

### Issue 4: "You have included the Google Maps JavaScript API multiple times"
**Solution**: Use the utility function we created to prevent multiple script loading.

## Next Steps

Once you have the basic map working, you can:

1. **Add Custom Markers**: Use custom icons and styling
2. **Add Info Windows**: Show detailed information when markers are clicked
3. **Add Map Controls**: Custom buttons and controls
4. **Handle User Location**: Get and display user's current location
5. **Add Drawing Tools**: Let users draw on the map
6. **Integrate with APIs**: Load marker data from your backend

## Best Practices

1. **Always handle loading states**: Show loading indicators while the map loads
2. **Handle errors gracefully**: Display user-friendly error messages
3. **Clean up resources**: Remove markers and event listeners when components unmount
4. **Secure your API key**: Use environment variables and restrict your API key
5. **Optimize performance**: Only load the map when needed (lazy loading)

This guide provides a solid foundation for integrating Google Maps into your Next.js application. Start with this basic example and gradually add more features as you become comfortable with the Google Maps API.
