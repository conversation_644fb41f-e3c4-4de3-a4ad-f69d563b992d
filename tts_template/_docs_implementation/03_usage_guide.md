# Usage Guide

## Getting Started

### Prerequisites
- Node.js 18+ 
- npm or yarn package manager

### Installation
```bash
cd poc_apps/tts_template
npm install
```

### Development
```bash
npm run dev
```
Open [http://localhost:3000](http://localhost:3000) in your browser.

### Build for Production
```bash
npm run build
npm start
```

## Using the Template

### Language Switching
1. Click the globe icon in the top-left header
2. Language switches between English and Arabic
3. Layout direction changes automatically (LTR ↔ RTL)
4. Preference is saved and persists across sessions

### Navigation

#### Desktop Navigation
- **Active Page**: Current page is highlighted in blue
- **Dropdown Menus**: "Reports" has a dropdown with sub-items
- **User Account**: Click user icon for account options
- **Breadcrumbs**: Shows current page location

#### Mobile Navigation
- **Hamburger Menu**: Tap the menu icon (☰) in the bottom navigation bar
- **Slide-out Panel**: Navigation opens from the right (LTR) or left (RTL)
- **Touch-Friendly**: Large touch targets for easy navigation
- **Backdrop Dismiss**: Tap outside the menu to close
- **Menu Sequence**: Same order maintained in both LTR and RTL modes

### Customization

#### Adding New Languages
1. Update the `translations` object in `contexts/LanguageContext.tsx`:
```typescript
const translations = {
  en: { /* English translations */ },
  ar: { /* Arabic translations */ },
  fr: { /* Add French translations */ }
};
```

2. Update the `Language` type:
```typescript
type Language = 'en' | 'ar' | 'fr';
```

3. Update the language switcher logic if needed.

#### Adding New Navigation Items
Update the `navigationItems` array in `components/Header.tsx`:
```typescript
const navigationItems = [
  { key: 'newItem', href: '/new-item' },
  // Add translation keys to LanguageContext
];
```

#### Customizing Colors
Update the CSS variables in `app/globals.css`:
```css
@theme inline {
  --color-primary-blue: #your-color;
  --color-secondary-blue: #your-color;
}
```

#### Adding New Pages
1. Create new page files in the `app/` directory
2. Use the `useLanguage` hook for translations:
```typescript
import { useLanguage } from '../contexts/LanguageContext';

export default function NewPage() {
  const { t } = useLanguage();
  return <div>{t('page.title')}</div>;
}
```

### Component Usage

#### Using the Language Hook
```typescript
import { useLanguage } from '../contexts/LanguageContext';

function MyComponent() {
  const { language, setLanguage, t, dir } = useLanguage();
  
  return (
    <div dir={dir}>
      <p>{t('my.translation.key')}</p>
      <button onClick={() => setLanguage('ar')}>
        Switch to Arabic
      </button>
    </div>
  );
}
```

#### Using shadcn/ui Components
```typescript
import { Button } from './ui/button';
import { DropdownMenu } from './ui/dropdown-menu';

function MyComponent() {
  return (
    <Button variant="ghost" size="sm">
      Click me
    </Button>
  );
}
```

## File Structure Guide

### Key Files to Modify
- `contexts/LanguageContext.tsx` - Add translations and languages
- `components/Header.tsx` - Modify navigation structure
- `components/Footer.tsx` - Update footer content
- `app/globals.css` - Customize colors and styles
- `app/page.tsx` - Main page content

### Adding New Components
1. Create in `components/` directory
2. Use the `useLanguage` hook for translations
3. Follow the existing naming conventions
4. Import and use shadcn/ui components as needed

### Styling Guidelines
- Use Tailwind CSS classes
- Follow the existing color scheme
- Use RTL-aware classes: `rtl:text-right`, `rtl:space-x-reverse`
- Test in both LTR and RTL modes

## Testing Checklist

### Functionality Testing
- [ ] Language switcher works
- [ ] Navigation items display correctly
- [ ] User dropdown functions
- [ ] Page loads without errors
- [ ] Responsive design works on mobile

### RTL/LTR Testing
- [ ] Text direction changes correctly
- [ ] Layout mirrors properly in RTL
- [ ] Icons and spacing adjust appropriately
- [ ] Navigation order maintained in RTL
- [ ] Mobile menu slides from correct side (left for RTL, right for LTR)
- [ ] Breadcrumb chevrons point in correct direction

### Mobile Testing
- [ ] Hamburger menu appears on mobile screens
- [ ] Mobile navigation panel slides in smoothly
- [ ] Touch targets are appropriately sized
- [ ] Backdrop dismissal works
- [ ] Menu items maintain correct sequence
- [ ] Responsive breakpoints work correctly
- [ ] Breadcrumbs display properly on mobile

### Browser Testing
- [ ] Chrome/Chromium
- [ ] Firefox
- [ ] Safari
- [ ] Edge

## Deployment

### Vercel (Recommended)
1. Connect your repository to Vercel
2. Deploy automatically on push to main branch

### Other Platforms
1. Build the project: `npm run build`
2. Deploy the `.next` folder and `package.json`
3. Ensure Node.js 18+ is available on the server

## Troubleshooting

### Common Issues
1. **Language not persisting**: Check localStorage in browser dev tools
2. **RTL not working**: Verify `dir` attribute on html element
3. **Components not found**: Check import paths and file structure
4. **Styling issues**: Verify Tailwind CSS compilation

### Debug Mode
Add console logs to the LanguageContext to debug language switching:
```typescript
const setLanguage = (lang: Language) => {
  console.log('Switching language to:', lang);
  setLanguageState(lang);
  localStorage.setItem('language', lang);
};
```
