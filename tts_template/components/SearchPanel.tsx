/**
 * SearchPanel Component
 * 
 * Provides search and filtering functionality for alerts.
 * Includes text search, type filter, status filter, and priority filter.
 * 
 * Props:
 * - onSearch: Callback function called when search/filters change
 * - loading: Boolean to show loading state
 */

import React, { useState, useCallback } from 'react';
import { Search, Filter, X } from 'lucide-react';
import { SearchFilters } from '../api/shipment-alerts';

interface SearchPanelProps {
  onSearch: (filters: SearchFilters) => void;
  loading?: boolean;
}

export default function SearchPanel({ onSearch, loading = false }: SearchPanelProps) {
  const [query, setQuery] = useState('');
  const [type, setType] = useState('');
  const [status, setStatus] = useState('');
  const [priority, setPriority] = useState('');
  const [showFilters, setShowFilters] = useState(false);

  // Debounced search function
  const handleSearch = useCallback(() => {
    const filters: SearchFilters = {};
    
    if (query.trim()) filters.query = query.trim();
    if (type) filters.type = type;
    if (status) filters.status = status;
    if (priority) filters.priority = priority;
    
    onSearch(filters);
  }, [query, type, status, priority, onSearch]);

  // Trigger search when any filter changes
  React.useEffect(() => {
    const timeoutId = setTimeout(handleSearch, 100); // Reduced debounce for static data
    return () => clearTimeout(timeoutId);
  }, [handleSearch]);

  const clearFilters = () => {
    setQuery('');
    setType('');
    setStatus('');
    setPriority('');
    onSearch({});
  };

  const hasActiveFilters = query || type || status || priority;

  return (
    <div className="bg-white rounded-lg border p-6 mb-6">
      <div className="flex flex-col space-y-4">
        {/* Search Bar */}
        <div className="flex items-center space-x-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="Search alerts by title, description, shipment ID, or location..."
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              disabled={loading}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed"
            />
          </div>
          
          <button
            onClick={() => setShowFilters(!showFilters)}
            className={`
              flex items-center space-x-2 px-4 py-3 border rounded-lg transition-colors
              ${showFilters 
                ? 'bg-blue-50 border-blue-300 text-blue-700' 
                : 'border-gray-300 text-gray-700 hover:bg-gray-50'
              }
            `}
          >
            <Filter className="w-5 h-5" />
            <span>Filters</span>
          </button>

          {hasActiveFilters && (
            <button
              onClick={clearFilters}
              className="flex items-center space-x-2 px-4 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
            >
              <X className="w-5 h-5" />
              <span>Clear</span>
            </button>
          )}
        </div>

        {/* Filter Options */}
        {showFilters && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-4 border-t border-gray-200">
            {/* Type Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Alert Type
              </label>
              <select
                value={type}
                onChange={(e) => setType(e.target.value)}
                disabled={loading}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:opacity-50"
              >
                <option value="">All Types</option>
                <option value="critical">Critical</option>
                <option value="warning">Warning</option>
                <option value="info">Info</option>
              </select>
            </div>

            {/* Status Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Status
              </label>
              <select
                value={status}
                onChange={(e) => setStatus(e.target.value)}
                disabled={loading}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:opacity-50"
              >
                <option value="">All Statuses</option>
                <option value="pending">Pending</option>
                <option value="investigating">Investigating</option>
                <option value="resolved">Resolved</option>
                <option value="monitoring">Monitoring</option>
              </select>
            </div>

            {/* Priority Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Priority
              </label>
              <select
                value={priority}
                onChange={(e) => setPriority(e.target.value)}
                disabled={loading}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:opacity-50"
              >
                <option value="">All Priorities</option>
                <option value="high">High</option>
                <option value="medium">Medium</option>
                <option value="low">Low</option>
              </select>
            </div>
          </div>
        )}

        {/* Active Filters Display */}
        {hasActiveFilters && (
          <div className="flex flex-wrap gap-2 pt-2">
            {query && (
              <span className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800">
                Search: "{query}"
              </span>
            )}
            {type && (
              <span className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-green-100 text-green-800">
                Type: {type}
              </span>
            )}
            {status && (
              <span className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-purple-100 text-purple-800">
                Status: {status}
              </span>
            )}
            {priority && (
              <span className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-orange-100 text-orange-800">
                Priority: {priority}
              </span>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
