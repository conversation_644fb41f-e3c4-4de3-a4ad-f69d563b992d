/**
 * ViewToggle Component
 * 
 * Provides toggle buttons for switching between table and list views.
 * 
 * Props:
 * - view: Current view mode ('table' | 'list')
 * - onViewChange: Callback function when view changes
 */

import React from 'react';
import { Table, List } from 'lucide-react';

interface ViewToggleProps {
  view: 'table' | 'list';
  onViewChange: (view: 'table' | 'list') => void;
}

export default function ViewToggle({ view, onViewChange }: ViewToggleProps) {
  return (
    <div className="flex items-center bg-gray-100 rounded-lg p-1">
      <button
        onClick={() => onViewChange('list')}
        className={`
          flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-all duration-200
          ${view === 'list' 
            ? 'bg-white text-gray-900 shadow-sm' 
            : 'text-gray-600 hover:text-gray-900'
          }
        `}
      >
        <List className="w-4 h-4" />
        <span>List View</span>
      </button>
      
      <button
        onClick={() => onViewChange('table')}
        className={`
          flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-all duration-200
          ${view === 'table' 
            ? 'bg-white text-gray-900 shadow-sm' 
            : 'text-gray-600 hover:text-gray-900'
          }
        `}
      >
        <Table className="w-4 h-4" />
        <span>Table View</span>
      </button>
    </div>
  );
}
