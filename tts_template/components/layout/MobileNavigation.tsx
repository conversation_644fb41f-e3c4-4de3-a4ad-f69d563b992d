'use client';

import { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { usePathname } from 'next/navigation';
import { Menu, X, ChevronDown } from 'lucide-react';
import { Button } from '../ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '../ui/dropdown-menu';
import { useLanguage } from '../../contexts/LanguageContext';
import { navigationItems } from './Navbar';

export default function MobileNavigation() {
  const [isOpen, setIsOpen] = useState(false);
  const { t, dir } = useLanguage();
  const pathname = usePathname();

  const toggleMenu = () => {
    setIsOpen(!isOpen);
  };

  const closeMenu = () => {
    setIsOpen(false);
  };

  return (
    <>
      {/* Mobile Menu Button */}
      <Button
        variant="ghost"
        size="sm"
        onClick={toggleMenu}
        className="text-white hover:text-gray-200 hover:bg-white/10"
      >
        {isOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
      </Button>

      {/* Mobile Menu Overlay */}
      {isOpen && (
        <div className="fixed inset-0 z-50 md:hidden">
          {/* Backdrop */}
          <div 
            className="fixed inset-0 bg-black bg-opacity-50"
            onClick={closeMenu}
          />
          
          {/* Menu Panel */}
          <div className={`fixed top-0 ${dir === 'rtl' ? 'left-0' : 'right-0'} h-full w-80 max-w-[85vw] bg-white shadow-xl transform transition-transform duration-300 ease-in-out slide-in-right`}>
            {/* Menu Header */}
            <div className="flex items-center justify-between p-4 border-b border-gray-200">
              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                <div className="relative w-60 h-20 flex-shrink-0 logo-container">
                  <Image
                    src="/logo.png"
                    alt="Government Portal Logo"
                    fill
                    className="object-contain logo-image"
                    priority
                  />
                </div>
                
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={closeMenu}
                className="text-gray-500 hover:text-gray-700"
              >
                <X className="w-5 h-5" />
              </Button>
            </div>

            {/* Menu Items */}
            <nav className="p-4">
              <div className="space-y-2">
                {/* Maintain the same sequence for both LTR and RTL */}
                {navigationItems.map((item) => {
                  const isActive = pathname === item.href;
                  
                  return (
                    <div key={item.key}>
                      {item.hasDropdown ? (
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button
                              variant="ghost"
                              className={`w-full justify-between text-left px-3 py-3 rounded-lg text-sm font-medium ${
                                isActive
                                  ? 'bg-[#007bff] text-white hover:bg-[#0056b3]'
                                  : 'text-gray-700 hover:text-gray-900 hover:bg-gray-100'
                              }`}
                            >
                              <span>{t(`navigation.${item.key}`)}</span>
                              <ChevronDown className="w-4 h-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent 
                            align={dir === 'rtl' ? 'start' : 'end'} 
                            className="w-56"
                          >
                            <DropdownMenuItem onClick={closeMenu}>
                              <Link href="/reports" className="w-full">
                                Operational Reports
                              </Link>
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={closeMenu}>
                              <Link href="/reports" className="w-full">
                                Compliance Reports
                              </Link>
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={closeMenu}>
                              <Link href="/reports" className="w-full">
                                Security Reports
                              </Link>
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      ) : (
                        <Link href={item.href} onClick={closeMenu}>
                          <Button
                            variant="ghost"
                            className={`w-full justify-start text-left px-3 py-3 rounded-lg text-sm font-medium ${
                              isActive
                                ? 'bg-[#007bff] text-white hover:bg-[#0056b3]'
                                : 'text-gray-700 hover:text-gray-900 hover:bg-gray-100'
                            }`}
                          >
                            {t(`navigation.${item.key}`)}
                          </Button>
                        </Link>
                      )}
                    </div>
                  );
                })}
              </div>
            </nav>

            {/* Menu Footer */}
            <div className="absolute bottom-0 left-0 right-0 p-4 border-t border-gray-200 bg-gray-50">
              <div className="flex items-center justify-center space-x-2 rtl:space-x-reverse">
                <div className="relative w-8 h-8 flex-shrink-0 logo-container">
                 
                </div>
                <div className="text-xs text-gray-500">
                  Traking System v.1.0
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
