'use client';

import { Globe } from 'lucide-react';
import { Button } from '../ui/button';
import { useLanguage } from '../../contexts/LanguageContext';

export default function LanguageSwitcher() {
  const { language, setLanguage, t } = useLanguage();

  const toggleLanguage = () => {
    const newLanguage = language === 'en' ? 'ar' : 'en';
    setLanguage(newLanguage);
  };

  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={toggleLanguage}
      className="text-white hover:text-white hover:bg-white/10 flex items-center space-x-2 rtl:space-x-reverse"
    >
      <Globe className="w-4 h-4" />
      <span className="text-sm">
        {t('header.languageSwitch')}
      </span>
    </Button>
  );
}
