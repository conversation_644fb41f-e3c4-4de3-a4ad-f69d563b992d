'use client';

import Link from 'next/link';
import { useLanguage } from '../contexts/LanguageContext';

interface SimplePageTemplateProps {
  titleKey: string;
}

export default function SimplePageTemplate({ titleKey }: SimplePageTemplateProps) {
  const { t } = useLanguage();

  return (
    <div className="container mx-auto px-6 py-8">
      <div className="max-w-4xl mx-auto">
        <div className="text-center py-16">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            {t(titleKey)}
          </h1>
          <p className="text-lg text-gray-600 mb-8">
            This page is under development. Content will be added soon.
          </p>
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 max-w-md mx-auto mb-6">
            <p className="text-blue-800 text-sm">
              <strong>Navigation Test:</strong> This page demonstrates that the navigation system is working correctly.
            </p>
          </div>

          <div className="mt-6">
            <Link
              href="/demo"
              className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors font-medium"
            >
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
              {t('navigation.demo')}
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
