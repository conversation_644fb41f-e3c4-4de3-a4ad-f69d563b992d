export const en = {
  'header.language': 'English',
  'header.languageSwitch': 'Arabic',
  'header.userAccount': '<PERSON>',
  'header.profile': 'Profile',
  'header.settings': 'Settings',
  'header.logout': 'Logout',
  'header.agencyName': 'Zakat, Tax, Customs Authority',
  'header.agencyNameArabic': 'هيئة الزكاة والضريبة والجمارك',
  'navigation.locationMonitor': 'Location Monitor',
  'navigation.focusedTrips': 'Focused Trips',
  'navigation.myAssignedPorts': 'My Assigned Ports',
  'navigation.dashboard': 'Dashboard',
  'navigation.configuration': 'Configuration',
  'navigation.suspiciousTrips': 'Suspicious Trips',
  'navigation.reports': 'Reports',
  'navigation.demoTrip': 'Demo Trip',
  'navigation.demo': 'Demo',
  'navigation.listDemo': 'List Demo',
  'navigation.mapDemo': 'Map Demo',
  'footer.poweredBy': 'Powered by',
  'footer.technicalUnited': 'لابلاس سوفتوير',
  'footer.technicalUnitedArabic': 'Laplacesoftware',

  // Demo Trip Page
  'demoTrip.title': 'Demo Trip Management',
  'demoTrip.description': 'Comprehensive trip tracking and management system',
  'demoTrip.backToTable': 'Back to Trip Table',
  'demoTrip.tripDetails': 'Trip Details',
  'demoTrip.tripNumber': 'Trip Number',
  'demoTrip.status': 'Status',
  'demoTrip.type': 'Type',
  'demoTrip.driver': 'Driver',
  'demoTrip.vehicle': 'Vehicle',
  'demoTrip.route': 'Route',
  'demoTrip.shipment': 'Shipment',
  'demoTrip.tracking': 'Tracking',
  'demoTrip.compliance': 'Compliance',
  'demoTrip.moreInfo': 'More Info',
  'demoTrip.progress': 'Progress',
  'demoTrip.estimatedArrival': 'Estimated Arrival',
  'demoTrip.creationDate': 'Creation Date',
  'demoTrip.activationDate': 'Activation Date',
  'demoTrip.completionDate': 'Completion Date'
} as const;
